# 想法记录工具

一个基于 Next.js 和 TypeScript 构建的现代化想法管理应用，帮助您记录、组织和管理创意想法。

## 🌟 功能特性

### 核心功能
- **想法管理**：创建、编辑、删除和查看想法
- **分类系统**：6个预设分类（工作、生活、学习、创业、技术、其他）
- **标签系统**：多标签支持，灵活标记想法
- **优先级管理**：低、中、高、紧急四个优先级
- **状态跟踪**：草稿、进行中、已完成、已归档

### 搜索与筛选
- **全文搜索**：在标题和内容中搜索关键词
- **分类筛选**：按分类快速筛选想法
- **标签筛选**：支持多标签组合筛选
- **优先级筛选**：按优先级筛选
- **状态筛选**：按状态筛选
- **高级筛选**：组合多种筛选条件

### 用户体验
- **响应式设计**：完美适配桌面端和移动端
- **数据持久化**：本地存储，数据不丢失
- **实时统计**：显示各状态想法数量
- **直观界面**：清晰的卡片式布局
- **快速操作**：便捷的编辑和删除功能

## 🛠️ 技术栈

- **框架**：Next.js 14 (App Router)
- **语言**：TypeScript
- **样式**：Tailwind CSS
- **组件库**：Radix UI
- **状态管理**：React Hooks + Local Storage
- **测试**：Vitest + Testing Library
- **E2E测试**：Playwright

## 📱 界面预览

### 主界面
- 统计卡片显示各状态想法数量
- 搜索和筛选工具栏
- 响应式网格布局的想法卡片

### 想法卡片
- 标题和分类显示
- 内容预览（最多3行）
- 标签展示
- 优先级和状态标识
- 创建时间显示
- 快速操作菜单

### 表单界面
- 标题和内容输入
- 分类选择
- 优先级设置
- 状态管理
- 多标签选择

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 运行测试
```bash
npm test
```

## 📁 项目结构

```
src/
├── app/
│   ├── ideas/           # 想法记录页面
│   └── globals.css      # 全局样式
├── components/
│   ├── ideas/           # 想法相关组件
│   │   ├── ideas-dashboard.tsx    # 主仪表板
│   │   ├── idea-card.tsx         # 想法卡片
│   │   ├── idea-form.tsx         # 想法表单
│   │   ├── idea-detail.tsx       # 想法详情
│   │   └── idea-filters.tsx      # 筛选器
│   └── layout/          # 布局组件
├── hooks/
│   └── use-ideas.ts     # 想法管理Hook
├── types/
│   └── ideas.ts         # 类型定义
└── test/
    └── ideas.test.tsx   # 测试文件
```

## 🎯 使用指南

### 创建想法
1. 点击"新建想法"按钮
2. 填写标题和内容
3. 选择分类和优先级
4. 设置状态
5. 选择相关标签
6. 点击"创建"保存

### 管理想法
- **查看详情**：点击想法标题
- **编辑想法**：点击卡片右上角菜单选择"编辑"
- **删除想法**：点击卡片右上角菜单选择"删除"

### 搜索筛选
- **搜索**：在搜索框输入关键词
- **快速筛选**：使用下拉菜单选择分类、优先级、状态
- **高级筛选**：点击"高级筛选"使用标签筛选
- **清除筛选**：点击"清除筛选"重置所有条件

## 🔧 自定义配置

### 添加新分类
在 `src/types/ideas.ts` 中的 `DEFAULT_CATEGORIES` 数组添加新分类：

```typescript
{
  id: '7',
  name: '新分类',
  color: '#颜色代码',
  description: '分类描述'
}
```

### 添加新标签
在 `src/types/ideas.ts` 中的 `DEFAULT_TAGS` 数组添加新标签：

```typescript
{
  id: '7',
  name: '新标签',
  color: '#颜色代码'
}
```

## 📊 数据存储

应用使用浏览器的 localStorage 进行数据持久化：
- 存储键：`ideas-app-data`
- 包含：想法列表、分类、标签
- 自动备份：每次操作后自动保存

## 🎨 设计系统

### 颜色方案
- **主色调**：蓝色系 (#3B82F6)
- **成功色**：绿色系 (#10B981)
- **警告色**：橙色系 (#F59E0B)
- **错误色**：红色系 (#EF4444)
- **中性色**：灰色系

### 响应式断点
- **移动端**：< 768px
- **平板端**：768px - 1024px
- **桌面端**：> 1024px

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🔮 未来计划

- [ ] 云端同步功能
- [ ] 团队协作功能
- [ ] 想法模板系统
- [ ] 数据导出功能
- [ ] 深色模式支持
- [ ] 想法关联功能
- [ ] 提醒和通知
- [ ] 数据统计分析

---

**开始使用想法记录工具，让您的创意想法得到更好的管理和实现！** 🚀
