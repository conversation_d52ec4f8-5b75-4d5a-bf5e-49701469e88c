'use client'

import { Idea } from '@/types/ideas'
import { formatDistanceToNow, format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface IdeaDetailProps {
  idea: Idea
  onEdit: (idea: Idea) => void
  onDelete: (id: string) => void
  onClose: () => void
}

const priorityColors = {
  low: 'bg-gray-100 text-gray-800',
  medium: 'bg-blue-100 text-blue-800',
  high: 'bg-orange-100 text-orange-800',
  urgent: 'bg-red-100 text-red-800',
}

const statusColors = {
  draft: 'bg-gray-100 text-gray-800',
  active: 'bg-green-100 text-green-800',
  completed: 'bg-blue-100 text-blue-800',
  archived: 'bg-yellow-100 text-yellow-800',
}

const priorityLabels = {
  low: '低',
  medium: '中',
  high: '高',
  urgent: '紧急',
}

const statusLabels = {
  draft: '草稿',
  active: '进行中',
  completed: '已完成',
  archived: '已归档',
}

export function IdeaDetail({ idea, onEdit, onDelete, onClose }: IdeaDetailProps) {
  const handleDelete = () => {
    if (confirm('确定要删除这个想法吗？')) {
      onDelete(idea.id)
      onClose()
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* 头部 */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{idea.title}</h1>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <span 
                    className="inline-block w-3 h-3 rounded-full"
                    style={{ backgroundColor: idea.category.color }}
                  />
                  <span>{idea.category.name}</span>
                </div>
                <span>创建于 {format(idea.createdAt, 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}</span>
                <span>更新于 {formatDistanceToNow(idea.updatedAt, { addSuffix: true, locale: zhCN })}</span>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <button
                onClick={() => onEdit(idea)}
                className="px-4 py-2 text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
              >
                编辑
              </button>
              <button
                onClick={handleDelete}
                className="px-4 py-2 text-red-600 bg-red-50 rounded-md hover:bg-red-100 transition-colors"
              >
                删除
              </button>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* 状态和优先级 */}
          <div className="flex items-center gap-3 mb-6">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${priorityColors[idea.priority]}`}>
              优先级：{priorityLabels[idea.priority]}
            </span>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${statusColors[idea.status]}`}>
              状态：{statusLabels[idea.status]}
            </span>
          </div>

          {/* 标签 */}
          {idea.tags.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2">标签</h3>
              <div className="flex flex-wrap gap-2">
                {idea.tags.map(tag => (
                  <span
                    key={tag.id}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                    style={{ 
                      backgroundColor: tag.color + '20', 
                      color: tag.color 
                    }}
                  >
                    {tag.name}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* 内容 */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">详细内容</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              {idea.content ? (
                <div className="prose prose-sm max-w-none">
                  {idea.content.split('\n').map((paragraph, index) => (
                    <p key={index} className="mb-2 last:mb-0 text-gray-700 leading-relaxed">
                      {paragraph}
                    </p>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 italic">暂无详细内容</p>
              )}
            </div>
          </div>

          {/* 附件 */}
          {idea.attachments && idea.attachments.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-3">附件</h3>
              <div className="space-y-2">
                {idea.attachments.map(attachment => (
                  <div key={attachment.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0">
                      {attachment.type === 'image' && (
                        <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                        </svg>
                      )}
                      {attachment.type === 'file' && (
                        <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clipRule="evenodd" />
                        </svg>
                      )}
                      {attachment.type === 'link' && (
                        <svg className="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{attachment.name}</p>
                      {attachment.size && (
                        <p className="text-xs text-gray-500">
                          {(attachment.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      )}
                    </div>
                    <a
                      href={attachment.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 transition-colors"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </a>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 分类信息 */}
          <div className="border-t border-gray-200 pt-4">
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <span>分类：</span>
                <div className="flex items-center gap-2">
                  <span 
                    className="inline-block w-3 h-3 rounded-full"
                    style={{ backgroundColor: idea.category.color }}
                  />
                  <span className="font-medium">{idea.category.name}</span>
                </div>
              </div>
              {idea.category.description && (
                <span className="text-gray-500">- {idea.category.description}</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
