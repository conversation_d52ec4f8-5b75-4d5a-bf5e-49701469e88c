'use client'

import { useState } from 'react'
import { Idea } from '@/types/ideas'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface IdeaCardProps {
  idea: Idea
  onEdit: (idea: Idea) => void
  onDelete: (id: string) => void
  onView: (idea: Idea) => void
}

const priorityColors = {
  low: 'bg-gray-100 text-gray-800',
  medium: 'bg-blue-100 text-blue-800',
  high: 'bg-orange-100 text-orange-800',
  urgent: 'bg-red-100 text-red-800',
}

const statusColors = {
  draft: 'bg-gray-100 text-gray-800',
  active: 'bg-green-100 text-green-800',
  completed: 'bg-blue-100 text-blue-800',
  archived: 'bg-yellow-100 text-yellow-800',
}

const priorityLabels = {
  low: '低',
  medium: '中',
  high: '高',
  urgent: '紧急',
}

const statusLabels = {
  draft: '草稿',
  active: '进行中',
  completed: '已完成',
  archived: '已归档',
}

export function IdeaCard({ idea, onEdit, onDelete, onView }: IdeaCardProps) {
  const [showMenu, setShowMenu] = useState(false)

  const handleDelete = () => {
    if (confirm('确定要删除这个想法吗？')) {
      onDelete(idea.id)
    }
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 md:p-6 hover:shadow-md transition-shadow">
      {/* 头部 */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1 min-w-0">
          <h3
            className="text-base md:text-lg font-semibold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors truncate"
            onClick={() => onView(idea)}
          >
            {idea.title}
          </h3>
          <div className="flex items-center gap-2 mt-1">
            <span
              className="inline-block w-3 h-3 rounded-full flex-shrink-0"
              style={{ backgroundColor: idea.category.color }}
            />
            <span className="text-sm text-gray-600 truncate">{idea.category.name}</span>
          </div>
        </div>
        
        {/* 操作菜单 */}
        <div className="relative">
          <button
            onClick={() => setShowMenu(!showMenu)}
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
            </svg>
          </button>
          
          {showMenu && (
            <div className="absolute right-0 mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-10">
              <button
                onClick={() => {
                  onEdit(idea)
                  setShowMenu(false)
                }}
                className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                编辑
              </button>
              <button
                onClick={() => {
                  handleDelete()
                  setShowMenu(false)
                }}
                className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-50"
              >
                删除
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 内容预览 */}
      <p className="text-gray-600 text-sm mb-4 line-clamp-3">
        {idea.content}
      </p>

      {/* 标签 */}
      {idea.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-4">
          {idea.tags.map(tag => (
            <span
              key={tag.id}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
              style={{ 
                backgroundColor: tag.color + '20', 
                color: tag.color 
              }}
            >
              {tag.name}
            </span>
          ))}
        </div>
      )}

      {/* 底部信息 */}
      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center gap-2">
          <span className={`px-2 py-1 rounded-full font-medium ${priorityColors[idea.priority]}`}>
            {priorityLabels[idea.priority]}
          </span>
          <span className={`px-2 py-1 rounded-full font-medium ${statusColors[idea.status]}`}>
            {statusLabels[idea.status]}
          </span>
        </div>
        
        <span className="text-gray-500">
          {formatDistanceToNow(idea.createdAt, { 
            addSuffix: true, 
            locale: zhCN 
          })}
        </span>
      </div>
    </div>
  )
}
