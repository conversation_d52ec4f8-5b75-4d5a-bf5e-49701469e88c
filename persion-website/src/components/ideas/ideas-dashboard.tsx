'use client'

import { useState, useMemo } from 'react'
import { useIdeas } from '@/hooks/use-ideas'
import { Idea, IdeaFilter } from '@/types/ideas'
import { IdeaCard } from './idea-card'
import { IdeaForm } from './idea-form'
import { IdeaDetail } from './idea-detail'
import { IdeaFilters } from './idea-filters'

export function IdeasDashboard() {
  const { ideas, categories, tags, loading, createIdea, updateIdea, deleteIdea, filterIdeas } = useIdeas()
  const [showForm, setShowForm] = useState(false)
  const [editingIdea, setEditingIdea] = useState<Idea | null>(null)
  const [viewingIdea, setViewingIdea] = useState<Idea | null>(null)
  const [filter, setFilter] = useState<IdeaFilter>({})

  // 筛选后的想法列表
  const filteredIdeas = useMemo(() => {
    return filterIdeas(filter)
  }, [filterIdeas, filter])

  // 统计信息
  const stats = useMemo(() => {
    return {
      total: ideas.length,
      draft: ideas.filter(idea => idea.status === 'draft').length,
      active: ideas.filter(idea => idea.status === 'active').length,
      completed: ideas.filter(idea => idea.status === 'completed').length,
      archived: ideas.filter(idea => idea.status === 'archived').length,
    }
  }, [ideas])

  const handleCreateIdea = (formData: any) => {
    createIdea(formData)
    setShowForm(false)
  }

  const handleUpdateIdea = (formData: any) => {
    if (editingIdea) {
      updateIdea(editingIdea.id, formData)
      setEditingIdea(null)
    }
  }

  const handleEditIdea = (idea: Idea) => {
    setEditingIdea(idea)
    setViewingIdea(null)
  }

  const handleViewIdea = (idea: Idea) => {
    setViewingIdea(idea)
  }

  const handleDeleteIdea = (id: string) => {
    deleteIdea(id)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div>
      {/* 统计卡片 */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 md:gap-4 mb-6">
        <div className="bg-white p-3 md:p-4 rounded-lg border border-gray-200">
          <div className="text-xl md:text-2xl font-bold text-gray-900">{stats.total}</div>
          <div className="text-xs md:text-sm text-gray-600">总计</div>
        </div>
        <div className="bg-white p-3 md:p-4 rounded-lg border border-gray-200">
          <div className="text-xl md:text-2xl font-bold text-gray-600">{stats.draft}</div>
          <div className="text-xs md:text-sm text-gray-600">草稿</div>
        </div>
        <div className="bg-white p-3 md:p-4 rounded-lg border border-gray-200">
          <div className="text-xl md:text-2xl font-bold text-green-600">{stats.active}</div>
          <div className="text-xs md:text-sm text-gray-600">进行中</div>
        </div>
        <div className="bg-white p-3 md:p-4 rounded-lg border border-gray-200">
          <div className="text-xl md:text-2xl font-bold text-blue-600">{stats.completed}</div>
          <div className="text-xs md:text-sm text-gray-600">已完成</div>
        </div>
        <div className="bg-white p-3 md:p-4 rounded-lg border border-gray-200 col-span-2 sm:col-span-1">
          <div className="text-xl md:text-2xl font-bold text-yellow-600">{stats.archived}</div>
          <div className="text-xs md:text-sm text-gray-600">已归档</div>
        </div>
      </div>

      {/* 筛选器 */}
      <IdeaFilters
        categories={categories}
        tags={tags}
        filter={filter}
        onFilterChange={setFilter}
      />

      {/* 操作栏 */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center gap-4">
          <h2 className="text-base md:text-lg font-semibold text-gray-900">
            想法列表 ({filteredIdeas.length})
          </h2>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          新建想法
        </button>
      </div>

      {/* 想法列表 */}
      {filteredIdeas.length === 0 ? (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">暂无想法</h3>
          <p className="mt-1 text-sm text-gray-500">
            {ideas.length === 0 ? '开始记录您的第一个想法吧！' : '没有符合筛选条件的想法'}
          </p>
          {ideas.length === 0 && (
            <div className="mt-6">
              <button
                onClick={() => setShowForm(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                新建想法
              </button>
            </div>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
          {filteredIdeas.map(idea => (
            <IdeaCard
              key={idea.id}
              idea={idea}
              onEdit={handleEditIdea}
              onDelete={handleDeleteIdea}
              onView={handleViewIdea}
            />
          ))}
        </div>
      )}

      {/* 表单模态框 */}
      {showForm && (
        <IdeaForm
          categories={categories}
          tags={tags}
          onSubmit={handleCreateIdea}
          onCancel={() => setShowForm(false)}
        />
      )}

      {/* 编辑表单模态框 */}
      {editingIdea && (
        <IdeaForm
          idea={editingIdea}
          categories={categories}
          tags={tags}
          onSubmit={handleUpdateIdea}
          onCancel={() => setEditingIdea(null)}
        />
      )}

      {/* 详情查看模态框 */}
      {viewingIdea && (
        <IdeaDetail
          idea={viewingIdea}
          onEdit={handleEditIdea}
          onDelete={handleDeleteIdea}
          onClose={() => setViewingIdea(null)}
        />
      )}
    </div>
  )
}
