'use client'

import { useState } from 'react'
import { IdeaFilter, Category, Tag, Priority, IdeaStatus } from '@/types/ideas'

interface IdeaFiltersProps {
  categories: Category[]
  tags: Tag[]
  filter: IdeaFilter
  onFilterChange: (filter: IdeaFilter) => void
}

const priorityOptions: { value: Priority; label: string }[] = [
  { value: 'low', label: '低' },
  { value: 'medium', label: '中' },
  { value: 'high', label: '高' },
  { value: 'urgent', label: '紧急' },
]

const statusOptions: { value: IdeaStatus; label: string }[] = [
  { value: 'draft', label: '草稿' },
  { value: 'active', label: '进行中' },
  { value: 'completed', label: '已完成' },
  { value: 'archived', label: '已归档' },
]

export function IdeaFilters({ categories, tags, filter, onFilterChange }: IdeaFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const handleSearchChange = (search: string) => {
    onFilterChange({ ...filter, search: search || undefined })
  }

  const handleCategoryChange = (categoryId: string) => {
    onFilterChange({ 
      ...filter, 
      category: categoryId === 'all' ? undefined : categoryId 
    })
  }

  const handleTagToggle = (tagId: string) => {
    const currentTags = filter.tags || []
    const newTags = currentTags.includes(tagId)
      ? currentTags.filter(id => id !== tagId)
      : [...currentTags, tagId]
    
    onFilterChange({ 
      ...filter, 
      tags: newTags.length > 0 ? newTags : undefined 
    })
  }

  const handlePriorityChange = (priority: string) => {
    onFilterChange({ 
      ...filter, 
      priority: priority === 'all' ? undefined : priority as Priority 
    })
  }

  const handleStatusChange = (status: string) => {
    onFilterChange({ 
      ...filter, 
      status: status === 'all' ? undefined : status as IdeaStatus 
    })
  }

  const clearFilters = () => {
    onFilterChange({})
  }

  const hasActiveFilters = filter.search || filter.category || filter.tags?.length || filter.priority || filter.status

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6">
      {/* 搜索框 */}
      <div className="mb-4">
        <div className="relative">
          <input
            type="text"
            placeholder="搜索想法..."
            value={filter.search || ''}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <svg 
            className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      {/* 快速筛选 */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2 mb-4">
        <select
          value={filter.category || 'all'}
          onChange={(e) => handleCategoryChange(e.target.value)}
          className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">所有分类</option>
          {categories.map(category => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>

        <select
          value={filter.priority || 'all'}
          onChange={(e) => handlePriorityChange(e.target.value)}
          className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">所有优先级</option>
          {priorityOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        <select
          value={filter.status || 'all'}
          onChange={(e) => handleStatusChange(e.target.value)}
          className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">所有状态</option>
          {statusOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="px-3 py-1 text-sm text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50 transition-colors"
        >
          {showAdvanced ? '收起' : '高级筛选'}
        </button>

        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="px-3 py-1 text-sm text-red-600 border border-red-300 rounded-md hover:bg-red-50 transition-colors col-span-2 sm:col-span-1"
          >
            清除筛选
          </button>
        )}
      </div>

      {/* 高级筛选 */}
      {showAdvanced && (
        <div className="border-t border-gray-200 pt-4">
          <div className="mb-3">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              标签筛选
            </label>
            <div className="flex flex-wrap gap-2">
              {tags.map(tag => (
                <button
                  key={tag.id}
                  onClick={() => handleTagToggle(tag.id)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    filter.tags?.includes(tag.id)
                      ? 'text-white'
                      : 'text-gray-600 bg-gray-100 hover:bg-gray-200'
                  }`}
                  style={{
                    backgroundColor: filter.tags?.includes(tag.id) ? tag.color : undefined
                  }}
                >
                  {tag.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 筛选结果提示 */}
      {hasActiveFilters && (
        <div className="text-sm text-gray-600">
          <span>当前筛选条件：</span>
          {filter.search && <span className="ml-1 text-blue-600">搜索"{filter.search}"</span>}
          {filter.category && (
            <span className="ml-1 text-blue-600">
              分类"{categories.find(c => c.id === filter.category)?.name}"
            </span>
          )}
          {filter.tags?.length && (
            <span className="ml-1 text-blue-600">
              标签({filter.tags.length}个)
            </span>
          )}
          {filter.priority && (
            <span className="ml-1 text-blue-600">
              优先级"{priorityOptions.find(p => p.value === filter.priority)?.label}"
            </span>
          )}
          {filter.status && (
            <span className="ml-1 text-blue-600">
              状态"{statusOptions.find(s => s.value === filter.status)?.label}"
            </span>
          )}
        </div>
      )}
    </div>
  )
}
