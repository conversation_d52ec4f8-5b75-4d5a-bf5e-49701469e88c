export interface Idea {
  id: string
  title: string
  content: string
  category: Category
  tags: Tag[]
  priority: Priority
  status: IdeaStatus
  createdAt: Date
  updatedAt: Date
  attachments?: Attachment[]
}

export interface Category {
  id: string
  name: string
  color: string
  description?: string
}

export interface Tag {
  id: string
  name: string
  color: string
}

export interface Attachment {
  id: string
  name: string
  type: 'image' | 'file' | 'link'
  url: string
  size?: number
}

export type Priority = 'low' | 'medium' | 'high' | 'urgent'

export type IdeaStatus = 'draft' | 'active' | 'completed' | 'archived'

export interface IdeaFilter {
  search?: string
  category?: string
  tags?: string[]
  priority?: Priority
  status?: IdeaStatus
  dateRange?: {
    start: Date
    end: Date
  }
}

export interface IdeaFormData {
  title: string
  content: string
  categoryId: string
  tagIds: string[]
  priority: Priority
  status: IdeaStatus
}

// 默认分类
export const DEFAULT_CATEGORIES: Category[] = [
  { id: '1', name: '工作', color: '#3B82F6', description: '工作相关的想法和创意' },
  { id: '2', name: '生活', color: '#10B981', description: '日常生活的想法和改进' },
  { id: '3', name: '学习', color: '#F59E0B', description: '学习和技能提升相关' },
  { id: '4', name: '创业', color: '#EF4444', description: '商业和创业想法' },
  { id: '5', name: '技术', color: '#8B5CF6', description: '技术和开发相关' },
  { id: '6', name: '其他', color: '#6B7280', description: '其他类型的想法' },
]

// 默认标签
export const DEFAULT_TAGS: Tag[] = [
  { id: '1', name: '紧急', color: '#EF4444' },
  { id: '2', name: '创新', color: '#3B82F6' },
  { id: '3', name: '改进', color: '#10B981' },
  { id: '4', name: '研究', color: '#F59E0B' },
  { id: '5', name: '实验', color: '#8B5CF6' },
  { id: '6', name: '灵感', color: '#EC4899' },
]
