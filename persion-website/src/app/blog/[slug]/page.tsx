import { notFound } from "next/navigation";
import { MDXRemote } from "next-mdx-remote/rsc";
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { format, parseISO } from 'date-fns';
import { Badge } from "@/components/ui/badge";

const contentDirectory = path.join(process.cwd(), 'content');

export async function generateStaticParams() {
  const postsDirectory = path.join(contentDirectory, 'blog');
  if (!fs.existsSync(postsDirectory)) return [];
  const filenames = fs.readdirSync(postsDirectory);
  return filenames.map(filename => ({
    slug: filename.replace(/\.mdx$/, '')
  }));
}

async function getPostBySlug(slug: string) {
  const fullPath = path.join(contentDirectory, 'blog', `${slug}.mdx`);
  if (!fs.existsSync(fullPath)) return null;

  const fileContents = fs.readFileSync(fullPath, 'utf8');
  const { data, content } = matter(fileContents);

  // Safe date formatting with error handling
  let formattedDate = '';
  if (data.date) {
    try {
      formattedDate = format(parseISO(data.date), 'MMMM d, yyyy');
    } catch (error) {
      console.warn(`Invalid date format in ${slug}.mdx:`, data.date);
      formattedDate = data.date; // Use original value as fallback
    }
  }

  return { 
    slug, 
    ...data, 
    date: formattedDate,
    content 
  } as {
    slug: string;
    title: string;
    summary: string;
    date: string;
    tags: string[];
    content: string;
  };
}

export async function generateMetadata({ params }: { params: { slug: string } }) {
  const post = await getPostBySlug(params.slug);
  if (!post) {
    return {
      title: 'Post Not Found'
    };
  }
  return { 
    title: `${post.title} | My Personal Website`,
    description: post.summary,
    openGraph: {
        title: post.title,
        description: post.summary,
    },
    twitter: {
        card: 'summary',
        title: post.title,
        description: post.summary,
    },
    alternates: {
      canonical: `/blog/${params.slug}`,
    },
  };
}

export default async function BlogPostPage({ params }: { params: { slug: string } }) {
  const post = await getPostBySlug(params.slug);

  if (!post) {
    notFound();
  }

  const { title, date, content, tags } = post;

  return (
    <article className="container mx-auto px-4 py-12">
      <header className="mb-8 text-center">
        <h1 className="text-4xl font-extrabold mb-2">{title}</h1>
        <p className="text-lg text-muted-foreground">{date}</p>
      </header>

      <div className="prose dark:prose-invert max-w-none mx-auto">
        <MDXRemote source={content} />
      </div>

      <footer className="mt-8 pt-8 border-t">
        <div className="flex flex-wrap gap-2">
          {tags.map((tag: string) => (
            <Badge key={tag} variant="secondary">{tag}</Badge>
          ))}
        </div>
      </footer>
    </article>
  );
} 
