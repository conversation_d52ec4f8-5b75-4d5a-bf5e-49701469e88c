'use client'

import { useState, useEffect, useCallback } from 'react'
import { Idea, IdeaFilter, IdeaFormData, Category, Tag, DEFAULT_CATEGORIES, DEFAULT_TAGS } from '@/types/ideas'

const STORAGE_KEY = 'ideas-app-data'

interface IdeasData {
  ideas: Idea[]
  categories: Category[]
  tags: Tag[]
}

// 示例数据
const SAMPLE_IDEAS: Idea[] = [
  {
    id: '1',
    title: '开发一个时间管理应用',
    content: '创建一个结合番茄工作法和GTD方法的时间管理应用。主要功能包括：\n- 任务分解和优先级管理\n- 番茄钟计时器\n- 数据统计和分析\n- 跨平台同步',
    category: { id: '5', name: '技术', color: '#8B5CF6', description: '技术和开发相关' },
    tags: [
      { id: '2', name: '创新', color: '#3B82F6' },
      { id: '5', name: '实验', color: '#8B5CF6' }
    ],
    priority: 'high',
    status: 'active',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20'),
  },
  {
    id: '2',
    title: '学习新的编程语言',
    content: '考虑学习Rust或Go语言来扩展技能栈。重点关注：\n- 系统编程能力\n- 并发处理\n- 性能优化',
    category: { id: '3', name: '学习', color: '#F59E0B', description: '学习和技能提升相关' },
    tags: [
      { id: '4', name: '研究', color: '#F59E0B' }
    ],
    priority: 'medium',
    status: 'draft',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10'),
  },
  {
    id: '3',
    title: '优化工作流程',
    content: '分析当前的工作流程，找出可以自动化的环节。考虑使用工具来提高效率。',
    category: { id: '1', name: '工作', color: '#3B82F6', description: '工作相关的想法和创意' },
    tags: [
      { id: '3', name: '改进', color: '#10B981' }
    ],
    priority: 'medium',
    status: 'completed',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-18'),
  }
]

export function useIdeas() {
  const [data, setData] = useState<IdeasData>({
    ideas: [],
    categories: DEFAULT_CATEGORIES,
    tags: DEFAULT_TAGS,
  })
  const [loading, setLoading] = useState(true)

  // 从本地存储加载数据
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const parsedData = JSON.parse(stored)
        // 转换日期字符串为Date对象
        parsedData.ideas = parsedData.ideas.map((idea: any) => ({
          ...idea,
          createdAt: new Date(idea.createdAt),
          updatedAt: new Date(idea.updatedAt),
        }))
        setData(parsedData)
      }
    } catch (error) {
      console.error('Failed to load ideas from storage:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  // 保存数据到本地存储
  const saveData = useCallback((newData: IdeasData) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newData))
      setData(newData)
    } catch (error) {
      console.error('Failed to save ideas to storage:', error)
    }
  }, [])

  // 创建新想法
  const createIdea = useCallback((formData: IdeaFormData) => {
    const newIdea: Idea = {
      id: Date.now().toString(),
      title: formData.title,
      content: formData.content,
      category: data.categories.find(c => c.id === formData.categoryId) || data.categories[0],
      tags: data.tags.filter(t => formData.tagIds.includes(t.id)),
      priority: formData.priority,
      status: formData.status,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    const newData = {
      ...data,
      ideas: [newIdea, ...data.ideas],
    }
    saveData(newData)
    return newIdea
  }, [data, saveData])

  // 更新想法
  const updateIdea = useCallback((id: string, formData: Partial<IdeaFormData>) => {
    const newData = {
      ...data,
      ideas: data.ideas.map(idea => {
        if (idea.id === id) {
          return {
            ...idea,
            ...formData,
            category: formData.categoryId 
              ? data.categories.find(c => c.id === formData.categoryId) || idea.category
              : idea.category,
            tags: formData.tagIds 
              ? data.tags.filter(t => formData.tagIds.includes(t.id))
              : idea.tags,
            updatedAt: new Date(),
          }
        }
        return idea
      }),
    }
    saveData(newData)
  }, [data, saveData])

  // 删除想法
  const deleteIdea = useCallback((id: string) => {
    const newData = {
      ...data,
      ideas: data.ideas.filter(idea => idea.id !== id),
    }
    saveData(newData)
  }, [data, saveData])

  // 筛选想法
  const filterIdeas = useCallback((filter: IdeaFilter) => {
    return data.ideas.filter(idea => {
      if (filter.search && !idea.title.toLowerCase().includes(filter.search.toLowerCase()) && 
          !idea.content.toLowerCase().includes(filter.search.toLowerCase())) {
        return false
      }
      if (filter.category && idea.category.id !== filter.category) {
        return false
      }
      if (filter.tags && filter.tags.length > 0 && 
          !filter.tags.some(tagId => idea.tags.some(tag => tag.id === tagId))) {
        return false
      }
      if (filter.priority && idea.priority !== filter.priority) {
        return false
      }
      if (filter.status && idea.status !== filter.status) {
        return false
      }
      if (filter.dateRange) {
        const ideaDate = idea.createdAt
        if (ideaDate < filter.dateRange.start || ideaDate > filter.dateRange.end) {
          return false
        }
      }
      return true
    })
  }, [data.ideas])

  return {
    ideas: data.ideas,
    categories: data.categories,
    tags: data.tags,
    loading,
    createIdea,
    updateIdea,
    deleteIdea,
    filterIdeas,
  }
}
