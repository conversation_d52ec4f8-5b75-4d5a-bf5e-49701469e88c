import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { IdeasDashboard } from '@/components/ideas/ideas-dashboard'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock confirm dialog
Object.defineProperty(window, 'confirm', {
  value: vi.fn(() => true)
})

describe('Ideas Dashboard', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear()
    localStorageMock.setItem.mockClear()
    localStorageMock.removeItem.mockClear()
    localStorageMock.clear.mockClear()
  })

  it('renders the dashboard with sample data', async () => {
    localStorageMock.getItem.mockReturnValue(null)

    render(<IdeasDashboard />)

    // 等待加载完成
    await waitFor(() => {
      expect(screen.getByText('总计')).toBeInTheDocument()
    })

    // 检查统计卡片
    expect(screen.getByText('总计')).toBeInTheDocument()
    expect(screen.getByText('草稿')).toBeInTheDocument()
    expect(screen.getByText('进行中')).toBeInTheDocument()
    expect(screen.getByText('已完成')).toBeInTheDocument()
    expect(screen.getByText('已归档')).toBeInTheDocument()

    // 检查示例想法
    expect(screen.getByText('开发一个时间管理应用')).toBeInTheDocument()
    expect(screen.getByText('学习新的编程语言')).toBeInTheDocument()
    expect(screen.getByText('优化工作流程')).toBeInTheDocument()
  })

  it('opens create form when clicking new idea button', async () => {
    localStorageMock.getItem.mockReturnValue(null)

    render(<IdeasDashboard />)

    await waitFor(() => {
      expect(screen.getByText('总计')).toBeInTheDocument()
    })

    // 点击新建想法按钮
    const newIdeaButton = screen.getByText('新建想法')
    fireEvent.click(newIdeaButton)

    // 检查表单是否打开
    await waitFor(() => {
      expect(screen.getByPlaceholderText('输入想法标题...')).toBeInTheDocument()
    })
  })

  it('filters ideas by search term', async () => {
    localStorageMock.getItem.mockReturnValue(null)

    render(<IdeasDashboard />)

    await waitFor(() => {
      expect(screen.getByText('总计')).toBeInTheDocument()
    })

    // 输入搜索词
    const searchInput = screen.getByPlaceholderText('搜索想法...')
    fireEvent.change(searchInput, { target: { value: '时间管理' } })

    // 检查筛选结果
    await waitFor(() => {
      expect(screen.getByText('开发一个时间管理应用')).toBeInTheDocument()
      expect(screen.queryByText('学习新的编程语言')).not.toBeInTheDocument()
      expect(screen.queryByText('优化工作流程')).not.toBeInTheDocument()
    })
  })

  it('filters ideas by category', async () => {
    localStorageMock.getItem.mockReturnValue(null)

    render(<IdeasDashboard />)

    await waitFor(() => {
      expect(screen.getByText('总计')).toBeInTheDocument()
    })

    // 选择技术分类
    const categorySelect = screen.getByDisplayValue('所有分类')
    fireEvent.change(categorySelect, { target: { value: '5' } }) // 技术分类ID

    // 检查筛选结果
    await waitFor(() => {
      expect(screen.getByText('开发一个时间管理应用')).toBeInTheDocument()
      expect(screen.queryByText('学习新的编程语言')).not.toBeInTheDocument()
      expect(screen.queryByText('优化工作流程')).not.toBeInTheDocument()
    })
  })
})
